#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出彩色文本的函数
echo_color() {
  case $1 in
    "red") echo -e "${RED}$2${NC}" ;;
    "green") echo -e "${GREEN}$2${NC}" ;;
    "yellow") echo -e "${YELLOW}$2${NC}" ;;
    "blue") echo -e "${BLUE}$2${NC}" ;;
    *) echo "$2" ;;
  esac
}

# 显示标题
echo_color "blue" "====================================="
echo_color "blue" "     前端调试脚本"
echo_color "blue" "====================================="

# 检查容器是否运行
if ! docker ps | grep -q blog-app; then
  echo_color "red" "❌ 容器未运行，请先启动容器"
  exit 1
fi

# 检查前端文件
echo_color "blue" "检查前端文件..."
docker exec blog-app ls -la /usr/share/nginx/html/

# 检查index.html
echo_color "blue" "检查index.html内容..."
docker exec blog-app cat /usr/share/nginx/html/index.html | head -20

# 检查JavaScript文件
echo_color "blue" "检查JavaScript文件..."
docker exec blog-app ls -la /usr/share/nginx/html/static/js/

# 检查CSS文件
echo_color "blue" "检查CSS文件..."
docker exec blog-app ls -la /usr/share/nginx/html/static/css/

# 检查Nginx配置
echo_color "blue" "检查Nginx配置..."
docker exec blog-app cat /etc/nginx/conf.d/default.conf | grep -A 5 "location / {"

# 检查Nginx日志
echo_color "blue" "检查Nginx访问日志..."
docker exec blog-app tail -n 10 /var/log/nginx/access.log

echo_color "blue" "检查Nginx错误日志..."
docker exec blog-app tail -n 10 /var/log/nginx/error.log

# 检查控制台错误
echo_color "yellow" "请在浏览器中打开开发者工具，查看控制台是否有JavaScript错误"
echo_color "yellow" "如果有错误，请提供错误信息以便进一步排查"
