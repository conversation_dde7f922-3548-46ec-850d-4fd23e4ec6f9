#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出彩色文本的函数
echo_color() {
  case $1 in
    "red") echo -e "${RED}$2${NC}" ;;
    "green") echo -e "${GREEN}$2${NC}" ;;
    "yellow") echo -e "${YELLOW}$2${NC}" ;;
    "blue") echo -e "${BLUE}$2${NC}" ;;
    *) echo "$2" ;;
  esac
}

# 显示标题
echo_color "blue" "====================================="
echo_color "blue"    "部署脚本"
echo_color "blue" "====================================="

# 备份数据库（如果容器正在运行）
if docker ps | grep -q blog-mysql; then
  echo_color "blue" "正在备份数据库..."
  mkdir -p ./backups
  docker exec blog-mysql mysqldump -u root -proot mysql_blog > ./backups/blog_backup_$(date +%Y%m%d_%H%M%S).sql
  echo_color "green" "数据库备份完成"
fi

# 停止并删除旧容器（如果存在）
echo_color "blue" "正在停止旧容器..."
docker-compose down

# 清理Docker缓存（可选）
echo_color "blue" "正在清理Docker缓存..."
docker system prune -f

# 重新构建并启动容器
echo_color "blue" "正在重新构建并启动容器..."
docker-compose up -d --build

# 等待服务启动
echo_color "blue" "等待服务启动..."
sleep 10

# 检查服务是否正常运行
if docker ps | grep -q blog-app && docker ps | grep -q blog-mysql; then
  echo_color "green" "✅ 部署成功完成！"
  echo_color "green" "应用现在可以通过以下地址访问:"
  echo_color "green" "http://localhost:8080"

  # 测试API连接
  echo_color "blue" "测试API连接..."
  if docker exec blog-app curl -s http://localhost:9000 > /dev/null; then
    echo_color "green" "✅ 后端API服务通过localhost正常运行"
  elif docker exec blog-app curl -s http://127.0.0.1:9000 > /dev/null; then
    echo_color "green" "✅ 后端API服务通过127.0.0.1正常运行"
  else
    echo_color "red" "❌ 后端API服务无法访问"
    echo_color "yellow" "尝试查看后端日志..."
    docker exec blog-app ps aux | grep server
  fi

  # 测试Nginx代理
  echo_color "blue" "测试Nginx代理..."
  if docker exec blog-app curl -s http://localhost/api/ > /dev/null; then
    echo_color "green" "✅ Nginx代理通过localhost正常工作"
  elif docker exec blog-app curl -s http://127.0.0.1/api/ > /dev/null; then
    echo_color "green" "✅ Nginx代理通过127.0.0.1正常工作"
  else
    echo_color "red" "❌ Nginx代理无法访问后端API"
    echo_color "yellow" "尝试查看Nginx配置和日志..."
    docker exec blog-app nginx -t
    docker exec blog-app cat /etc/nginx/conf.d/default.conf | grep proxy_pass
  fi

  # 显示容器日志
  echo_color "blue" "显示应用容器日志（按Ctrl+C退出）:"
  docker logs -f blog-app
else
  echo_color "red" "❌ 部署可能存在问题，请检查容器日志:"
  echo_color "yellow" "docker logs blog-app"
  echo_color "yellow" "docker logs blog-mysql"
fi
