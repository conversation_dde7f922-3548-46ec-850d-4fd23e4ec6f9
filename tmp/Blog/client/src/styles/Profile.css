/* Profile Page Styles */

/* Profile sidebar */
.profile-sidebar {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.dark-card {
  background-color: var(--card-bg);
  border-color: var(--border-color);
}

/* Avatar container and styling */
.avatar-container {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  border: 3px solid var(--accent-color);
}

.profile-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 50%;
}

.avatar-container:hover .avatar-overlay {
  opacity: 1;
}

.avatar-edit-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--accent-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-edit-btn:hover {
  transform: scale(1.1);
  background: var(--accent-color-hover);
}

/* Profile username and email */
.profile-username {
  margin-top: 1rem;
  margin-bottom: 0.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.profile-email {
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  color: var(--text-secondary);
}

/* Profile stats */
.profile-stats {
  display: flex;
  justify-content: center;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.stat-item {
  text-align: center;
  padding: 0 1rem;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--accent-color);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Profile content card */
.profile-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  height: 100%;
}

/* Profile tabs */
.profile-tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 1.5rem;
}

.profile-tabs .nav-link {
  color: var(--text-secondary);
  border: none;
  padding: 0.75rem 1.25rem;
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.profile-tabs .nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--accent-color);
  transition: width 0.3s ease;
}

.profile-tabs .nav-link:hover {
  color: var(--text-primary);
}

.profile-tabs .nav-link.active {
  color: var(--accent-color);
  background: transparent;
  border: none;
}

.profile-tabs .nav-link.active::after {
  width: 100%;
}

/* User posts list */
.user-posts {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.post-item {
  padding: 1.25rem;
  border-radius: 8px;
  background-color: var(--bg-secondary);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.post-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.post-title {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.post-excerpt {
  color: var(--text-secondary);
  margin-bottom: 1rem;
  font-size: 0.9rem;
  line-height: 1.5;
}

.post-actions {
  display: flex;
  gap: 0.5rem;
}

/* Form styling */
.form-label {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.form-control {
  border-radius: 8px;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg);
  color: var(--input-text);
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: var(--accent-color);
  box-shadow: 0 0 0 3px rgba(var(--accent-color-rgb), 0.15);
}

.form-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* Button styling */
.btn-primary {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  padding: 0.5rem 1.5rem;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: var(--accent-color-hover);
  border-color: var(--accent-color-hover);
  transform: translateY(-2px);
}

.btn-outline-primary {
  color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-outline-primary:hover {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.btn-outline-secondary {
  color: var(--text-secondary);
  border-color: var(--border-color);
}

.btn-outline-secondary:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* Progress bar */
.progress {
  height: 8px;
  border-radius: 4px;
  background-color: var(--bg-secondary);
  margin-top: 0.5rem;
}

.progress-bar {
  background-color: var(--accent-color);
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-sidebar {
    margin-bottom: 1.5rem;
  }
  
  .avatar-container {
    width: 100px;
    height: 100px;
  }
  
  .profile-tabs .nav-link {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
}
