# Docker 部署指南

本文档提供了使用 Docker 和 Nginx 部署博客应用的详细步骤。

## 部署架构

该部署方案使用以下组件：

- **Nginx**: 作为前端服务器，提供静态文件服务并代理后端 API 请求
- **Go 后端**: 提供 API 服务
- **MySQL**: 数据库服务
- **Docker**: 容器化所有服务
- **Docker Compose**: 编排和管理容器

## 前提条件

- 安装 Docker (20.10.0+)
- 安装 Docker Compose (2.0.0+)
- 服务器至少 1GB 内存
- 开放 80 端口（HTTP）

## 部署步骤

### 1. 准备服务器

首先，确保服务器已安装 Docker 和 Docker Compose：

```bash
# 安装 Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装 Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.18.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 克隆代码库

```bash
git clone https://github.com/zhuzhuc/Go.git
cd Go/Blog
```

### 3. 部署应用

使用部署脚本一键部署：

```bash
chmod +x deploy-prod.sh
./deploy-prod.sh
```

或者手动执行以下步骤：

```bash
# 构建并启动容器
docker-compose up -d --build
```

### 4. 验证部署

部署完成后，您可以通过服务器 IP 地址访问应用：

```
http://your_server_ip
```

### 5. 查看日志

如果需要查看应用日志，可以使用以下命令：

```bash
# 查看所有容器日志
docker-compose logs

# 查看特定容器日志
docker-compose logs app
docker-compose logs db
```

## 配置说明

### Docker Compose 配置

`docker-compose.yml` 文件定义了两个服务：

1. **db**: MySQL 数据库服务
   - 使用官方 MySQL 8.0 镜像
   - 数据持久化存储在 `mysql_data` 卷中
   - 暴露 3306 端口（可选，用于外部访问）

2. **app**: 应用服务（前端 + 后端）
   - 使用自定义 Dockerfile 构建
   - 前端由 Nginx 提供服务
   - 后端 Go 服务运行在同一容器中
   - 上传文件存储在 `uploads_data` 卷中
   - 暴露 80 端口用于 HTTP 访问

### Nginx 配置

`nginx/default.conf` 文件配置了 Nginx 服务器：

- 前端静态文件服务
- 后端 API 代理（/api/ 路径）
- 上传文件目录
- 静态资源缓存设置
- 错误页面处理

### 环境变量

`server/.env.prod` 文件包含了后端服务的环境变量：

- 数据库连接信息
- 其他配置参数

## 维护和更新

### 更新应用

当代码有更新时，可以使用以下命令更新应用：

```bash
git pull
./deploy-prod.sh
```

### 备份数据库

部署脚本会自动备份数据库，备份文件存储在 `./backups` 目录中。

您也可以手动备份数据库：

```bash
docker exec blog-mysql mysqldump -u root -proot mysql_blog > ./backups/manual_backup_$(date +%Y%m%d).sql
```

### 恢复数据库

如果需要恢复数据库，可以使用以下命令：

```bash
cat ./backups/backup_file.sql | docker exec -i blog-mysql mysql -u root -proot mysql_blog
```

## 故障排除

### 应用无法访问

检查容器是否正在运行：

```bash
docker ps
```

如果容器未运行，查看日志：

```bash
docker-compose logs
```

### 数据库连接问题

确保数据库容器正在运行，并且环境变量配置正确：

```bash
# 检查数据库容器
docker ps | grep mysql

# 检查环境变量
cat server/.env.prod
```

### 文件权限问题

如果遇到上传文件权限问题，可以尝试：

```bash
docker exec -it blog-app chmod -R 777 /app/uploads
```

## 安全建议

1. 更改默认的数据库密码
2. 配置 HTTPS（需要额外的 Nginx 配置和 SSL 证书）
3. 限制数据库端口访问（移除 docker-compose.yml 中的 db 服务端口映射）
4. 定期备份数据
5. 保持容器和镜像更新

## 高级配置

### 配置 HTTPS

要配置 HTTPS，您需要：

1. 获取 SSL 证书
2. 修改 Nginx 配置
3. 开放 443 端口

详细步骤请参考 Nginx HTTPS 配置文档。

### 配置域名

如果您有域名，可以将其指向服务器 IP，并修改 Nginx 配置中的 `server_name` 指令。

### 水平扩展

如果需要处理更多流量，可以考虑：

1. 分离前端和后端服务
2. 使用负载均衡器
3. 数据库主从复制

## 结论

按照上述步骤，您应该能够成功地使用 Docker 和 Nginx 部署博客应用。如果遇到问题，请查看相关日志并参考故障排除部分。
