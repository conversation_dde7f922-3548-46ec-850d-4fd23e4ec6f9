package main

import (
	"fmt"
	"reflect"
)

type Processor interface {
	Process(data interface{})
}

type User struct {
	Name  string
	Age   int
	Meta  map[string]interface{}
	Admin *AdminInfo
}

type AdminInfo struct {
	Level string
	Tags  []string
}
type ComplexProcessor struct{}

func (p *ComplexProcessor) Process(data interface{}) {
	switch v := data.(type) {
	case string:
		fmt.Println("string:", v)
	case int:
		fmt.Println("int:", v)
	case map[string]interface{}:
		fmt.Println("map detected")
		for k, val := range v {
			fmt.Printf("  key: %s, value type: %s\n", k, reflect.TypeOf(val))
		}
	case User:
		fmt.Println("User struct detected")
		fmt.Println("  Name:", v.Name)
		fmt.Println("  Age:", v.Age)
		if v.Admin != nil {
			fmt.Println("  Admin Level:", v.Admin.Level)
		}
	default:
		val := reflect.ValueOf(data)
		t := val.Type()
		fmt.Printf("Unknown type: %s\n", t.String())
		if t.Kind() == reflect.Struct {
			fmt.Println("Reflecting struct fields:")
			for i := 0; i < t.NumField(); i++ {
				field := t.Field(i)
				fmt.Printf("  Field %s (%s)\n", field.Name, field.Type)
			}
		}
	}
}

func main() {
	processor := &ComplexProcessor{}
	processor.Process("hello world")
	processor.Process(123)
	processor.Process(map[string]interface{}{
		"flag":  true,
		"score": 99.5,
	})

	user := User{
		Name: "Alice",
		Age:  30,
		Meta: map[string]interface{}{
			"active": true,
			"login":  "2025-06-11",
		},
		Admin: &AdminInfo{
			Level: "super",
			Tags:  []string{"root", "trusted"},
		},
	}

	processor.Process(user)
	processor.Process(struct {
		X int
		Y string
	}{X: 42, Y: "hi"})
}
