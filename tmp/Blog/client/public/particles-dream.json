{"particles": {"number": {"value": 120, "density": {"enable": true, "value_area": 1200}}, "color": {"value": ["#9C27B0", "#673AB7", "#3F51B5", "#2196F3", "#03A9F4", "#00BCD4", "#009688"]}, "shape": {"type": "circle", "stroke": {"width": 0, "color": "#000000"}, "polygon": {"nb_sides": 5}, "image": {"src": "img/github.svg", "width": 100, "height": 100}}, "opacity": {"value": 0.6, "random": true, "anim": {"enable": true, "speed": 0.5, "opacity_min": 0.1, "sync": false}}, "size": {"value": 5, "random": true, "anim": {"enable": true, "speed": 1, "size_min": 0.1, "sync": false}}, "line_linked": {"enable": true, "distance": 150, "color": "#9C27B0", "opacity": 0.2, "width": 1}, "move": {"enable": true, "speed": 1.5, "direction": "none", "random": true, "straight": false, "out_mode": "out", "bounce": false, "attract": {"enable": true, "rotateX": 600, "rotateY": 1200}}}, "interactivity": {"detect_on": "window", "events": {"onhover": {"enable": true, "mode": "grab"}, "onclick": {"enable": true, "mode": "push"}, "resize": true}, "modes": {"grab": {"distance": 140, "line_linked": {"opacity": 0.8}}, "bubble": {"distance": 400, "size": 40, "duration": 2, "opacity": 8, "speed": 3}, "repulse": {"distance": 200, "duration": 0.4}, "push": {"particles_nb": 4}, "remove": {"particles_nb": 2}}}, "retina_detect": true}