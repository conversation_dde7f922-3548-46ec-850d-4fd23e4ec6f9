#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出彩色文本的函数
echo_color() {
  case $1 in
    "red") echo -e "${RED}$2${NC}" ;;
    "green") echo -e "${GREEN}$2${NC}" ;;
    "yellow") echo -e "${YELLOW}$2${NC}" ;;
    "blue") echo -e "${BLUE}$2${NC}" ;;
    *) echo "$2" ;;
  esac
}

# 显示标题
echo_color "blue" "====================================="
echo_color "blue" "     前端调试和修复脚本"
echo_color "blue" "====================================="

# 检查容器是否运行
if ! docker ps | grep -q blog-app; then
  echo_color "red" "❌ 容器未运行，请先启动容器"
  exit 1
fi

# 创建修复脚本
echo_color "blue" "创建容器内部修复脚本..."
cat > /tmp/fix-frontend.sh << 'EOF'
#!/bin/sh
set -e

echo "===== 开始修复前端 ====="

# 检查前端文件
echo "检查前端文件..."
ls -la /usr/share/nginx/html/

# 检查静态资源目录
echo "检查静态资源目录..."
ls -la /usr/share/nginx/html/static/js/
ls -la /usr/share/nginx/html/static/css/

# 检查index.html
echo "检查index.html内容..."
cat /usr/share/nginx/html/index.html

# 创建一个简单的测试页面
echo "创建测试页面..."
cat > /usr/share/nginx/html/test.html << 'EOT'
<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Test Page</h1>
    <p>If you can see this, Nginx is serving static files correctly.</p>
    <p>Try accessing the <a href="/static/js/main.2ddbfa9a.js">main JavaScript file</a> directly.</p>
    <p>Try accessing the <a href="/static/css/main.e7427fec.css">main CSS file</a> directly.</p>
</body>
</html>
EOT

# 修复index.html
echo "修复index.html..."
cat > /usr/share/nginx/html/index.html << 'EOT'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Personal Blog" />
    <link rel="manifest" href="/manifest.json" />
    <title>ZZ_Blog</title>
    <!-- 移除SRI完整性检查 -->
    <script>
        // 禁用SRI检查
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
                const src = e.target.src || e.target.href;
                if (src && src.includes('bootstrap')) {
                    console.log('Reloading resource without integrity check:', src);
                    const newElem = document.createElement(e.target.tagName);
                    for (const attr of e.target.attributes) {
                        if (attr.name !== 'integrity' && attr.name !== 'crossorigin') {
                            newElem.setAttribute(attr.name, attr.value);
                        }
                    }
                    e.target.parentNode.replaceChild(newElem, e.target);
                    return true;
                }
            }
        }, true);
    </script>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
</body>
</html>
EOT

# 重启Nginx
echo "重启Nginx..."
nginx -s reload

echo "===== 修复完成 ====="
echo "请访问 http://localhost:8080/test.html 测试静态文件服务是否正常"
echo "然后访问 http://localhost:8080 查看主页是否正常显示"
EOF

# 复制脚本到容器
echo_color "blue" "复制修复脚本到容器..."
docker cp /tmp/fix-frontend.sh blog-app:/tmp/fix-frontend.sh

# 在容器内执行修复脚本
echo_color "blue" "在容器内执行修复脚本..."
docker exec -it blog-app sh -c "chmod +x /tmp/fix-frontend.sh && /tmp/fix-frontend.sh"

# 清理临时文件
rm /tmp/fix-frontend.sh

echo_color "green" "修复操作完成"
echo_color "yellow" "请访问 http://localhost:8080/test.html 测试静态文件服务是否正常"
echo_color "yellow" "然后访问 http://localhost:8080 查看主页是否正常显示"
