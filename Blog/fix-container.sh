#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出彩色文本的函数
echo_color() {
  case $1 in
    "red") echo -e "${RED}$2${NC}" ;;
    "green") echo -e "${GREEN}$2${NC}" ;;
    "yellow") echo -e "${YELLOW}$2${NC}" ;;
    "blue") echo -e "${BLUE}$2${NC}" ;;
    *) echo "$2" ;;
  esac
}

# 显示标题
echo_color "blue" "====================================="
echo_color "blue" "     容器内部修复脚本"
echo_color "blue" "====================================="

# 检查容器是否运行
if ! docker ps | grep -q blog-app; then
  echo_color "red" "❌ 容器未运行，请先启动容器"
  exit 1
fi

# 创建修复脚本
echo_color "blue" "创建容器内部修复脚本..."
cat > /tmp/fix-inside.sh << 'EOF'
#!/bin/sh
set -e

echo "===== 开始修复 ====="

# 检查后端服务
echo "检查后端服务..."
if ps aux | grep -v grep | grep -q "/app/server/server"; then
  echo "✅ 后端服务正在运行"
else
  echo "❌ 后端服务未运行，尝试启动..."
  cd /app
  /app/server/server &
  sleep 2
  if ps aux | grep -v grep | grep -q "/app/server/server"; then
    echo "✅ 后端服务已启动"
  else
    echo "❌ 后端服务启动失败"
  fi
fi

# 测试后端API
echo "测试后端API..."
if curl -s http://localhost:9000 > /dev/null; then
  echo "✅ 后端API可通过localhost访问"
elif curl -s http://127.0.0.1:9000 > /dev/null; then
  echo "✅ 后端API可通过127.0.0.1访问"
else
  echo "❌ 后端API不可访问，尝试重启服务..."
  cd /app
  pkill -f "/app/server/server" || true
  sleep 1
  /app/server/server &
  sleep 3
  if curl -s http://localhost:9000 > /dev/null; then
    echo "✅ 重启后，后端API可访问"
  else
    echo "❌ 重启后，后端API仍不可访问"
  fi
fi

# 检查Nginx配置
echo "检查Nginx配置..."
nginx -t
if [ $? -eq 0 ]; then
  echo "✅ Nginx配置正确"
else
  echo "❌ Nginx配置有误，尝试修复..."
  # 备份原始配置
  cp /etc/nginx/conf.d/default.conf /etc/nginx/conf.d/default.conf.bak
  # 修改配置，使用localhost而不是127.0.0.1
  sed -i 's/127.0.0.1/localhost/g' /etc/nginx/conf.d/default.conf
  nginx -t
  if [ $? -eq 0 ]; then
    echo "✅ Nginx配置已修复"
  else
    echo "❌ Nginx配置修复失败，恢复备份..."
    cp /etc/nginx/conf.d/default.conf.bak /etc/nginx/conf.d/default.conf
  fi
fi

# 重启Nginx
echo "重启Nginx..."
nginx -s reload
if [ $? -eq 0 ]; then
  echo "✅ Nginx已重启"
else
  echo "❌ Nginx重启失败，尝试强制重启..."
  pkill -f nginx
  sleep 1
  nginx
  if [ $? -eq 0 ]; then
    echo "✅ Nginx已强制重启"
  else
    echo "❌ Nginx强制重启失败"
  fi
fi

# 测试Nginx代理
echo "测试Nginx代理..."
if curl -s http://localhost/api/ > /dev/null; then
  echo "✅ Nginx代理通过localhost正常工作"
elif curl -s http://127.0.0.1/api/ > /dev/null; then
  echo "✅ Nginx代理通过127.0.0.1正常工作"
else
  echo "❌ Nginx代理不可访问，检查日志..."
  tail -n 50 /var/log/nginx/error.log
fi

echo "===== 修复完成 ====="
EOF

# 复制脚本到容器
echo_color "blue" "复制修复脚本到容器..."
docker cp /tmp/fix-inside.sh blog-app:/tmp/fix-inside.sh

# 在容器内执行修复脚本
echo_color "blue" "在容器内执行修复脚本..."
docker exec -it blog-app sh -c "chmod +x /tmp/fix-inside.sh && /tmp/fix-inside.sh"

# 清理临时文件
rm /tmp/fix-inside.sh

echo_color "green" "修复操作完成"
