#!/bin/sh
set -e

echo "===== Debug info start ====="
id
ls -ld /app
ls -la /app/server
echo "===== Debug info end ====="

# 确保后端服务有执行权限
chmod +x /app/server/server

# 启动后端服务
cd /app
echo "Starting backend service..."
/app/server/server &
backend_pid=$!
echo "Backend service started with PID: $backend_pid"

# 等待后端服务启动
echo "Waiting for backend service to start..."
sleep 5

# 测试后端服务是否可访问
echo "Testing backend service..."
if curl -s http://localhost:9000 > /dev/null; then
    echo "Backend service is accessible."
else
    echo "WARNING: Backend service is not accessible via curl. This might cause issues."
    echo "Trying with 127.0.0.1 instead..."
    if curl -s http://127.0.0.1:9000 > /dev/null; then
        echo "Backend service is accessible via 127.0.0.1."
    else
        echo "WARNING: Backend service is still not accessible. Continuing anyway..."
    fi
fi

# 启动Nginx
echo "Starting Nginx..."
nginx -g "daemon on;"
sleep 2

# 测试Nginx配置
echo "Testing Nginx configuration..."
nginx -t || echo "WARNING: Nginx configuration test failed."

# 测试Nginx代理是否工作
echo "Testing Nginx proxy..."
if curl -s http://localhost/api/ > /dev/null; then
    echo "Nginx proxy to backend is working."
else
    echo "WARNING: Nginx proxy to backend is not working. Trying with 127.0.0.1..."
    if curl -s http://127.0.0.1/api/ > /dev/null; then
        echo "Nginx proxy is working via 127.0.0.1."
    else
        echo "WARNING: Nginx proxy is still not working. This might cause issues."
    fi
fi

# 检查后端服务是否正常运行
if ps -p $backend_pid > /dev/null; then
    echo "Backend service is running."
else
    echo "ERROR: Backend service failed to start!"
    exit 1
fi

# 保持容器运行
echo "All services started successfully."
wait

