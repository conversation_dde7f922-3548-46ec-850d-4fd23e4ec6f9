<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="Personal Blog" />
    <link rel="manifest" href="/manifest.json" />
    <title>ZZ_Blog</title>
    <!-- 移除SRI完整性检查 -->
    <script>
        // 禁用SRI检查
        window.addEventListener('error', function(e) {
            if (e.target.tagName === 'SCRIPT' || e.target.tagName === 'LINK') {
                const src = e.target.src || e.target.href;
                if (src && src.includes('bootstrap')) {
                    console.log('Reloading resource without integrity check:', src);
                    const newElem = document.createElement(e.target.tagName);
                    for (const attr of e.target.attributes) {
                        if (attr.name !== 'integrity' && attr.name !== 'crossorigin') {
                            newElem.setAttribute(attr.name, attr.value);
                        }
                    }
                    e.target.parentNode.replaceChild(newElem, e.target);
                    return true;
                }
            }
        }, true);
    </script>
    <!-- 确保加载React应用的JS和CSS文件 -->
    <link href="/static/css/main.e7427fec.css" rel="stylesheet">
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!-- 加载React应用的主JS文件 -->
    <script src="/static/js/main.2ddbfa9a.js"></script>
</body>
</html>
