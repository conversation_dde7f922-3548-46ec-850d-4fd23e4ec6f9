#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 输出彩色文本的函数
echo_color() {
  case $1 in
    "red") echo -e "${RED}$2${NC}" ;;
    "green") echo -e "${GREEN}$2${NC}" ;;
    "yellow") echo -e "${YELLOW}$2${NC}" ;;
    "blue") echo -e "${BLUE}$2${NC}" ;;
    *) echo "$2" ;;
  esac
}

# 显示标题
echo_color "blue" "====================================="
echo_color "blue" "     前端重新部署脚本"
echo_color "blue" "====================================="

# 检查容器是否运行
if ! docker ps | grep -q blog-app; then
  echo_color "red" "❌ 容器未运行，请先启动容器"
  exit 1
fi

# 重新构建前端
echo_color "blue" "重新构建前端..."
cd client
npm run build

# 检查构建是否成功
if [ ! -d "build" ]; then
  echo_color "red" "❌ 前端构建失败"
  exit 1
fi

# 复制构建产物到容器
echo_color "blue" "复制构建产物到容器..."
docker cp build/. blog-app:/usr/share/nginx/html/

# 创建测试页面
echo_color "blue" "创建测试页面..."
cat > /tmp/test.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Test Page</title>
</head>
<body>
    <h1>Test Page</h1>
    <p>If you can see this, Nginx is serving static files correctly.</p>
    <script>
        // 列出所有可用的JS和CSS文件
        fetch('/')
            .then(response => response.text())
            .then(html => {
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const scripts = Array.from(doc.querySelectorAll('script')).map(s => s.src);
                const styles = Array.from(doc.querySelectorAll('link[rel="stylesheet"]')).map(l => l.href);
                
                const scriptsList = document.createElement('div');
                scriptsList.innerHTML = '<h2>JavaScript Files:</h2><ul>' + 
                    scripts.map(s => `<li><a href="${s}">${s}</a></li>`).join('') + 
                    '</ul>';
                
                const stylesList = document.createElement('div');
                stylesList.innerHTML = '<h2>CSS Files:</h2><ul>' + 
                    styles.map(s => `<li><a href="${s}">${s}</a></li>`).join('') + 
                    '</ul>';
                
                document.body.appendChild(scriptsList);
                document.body.appendChild(stylesList);
            });
    </script>
</body>
</html>
EOF

# 复制测试页面到容器
docker cp /tmp/test.html blog-app:/usr/share/nginx/html/test.html

# 重启Nginx
echo_color "blue" "重启Nginx..."
docker exec blog-app nginx -s reload

# 清理临时文件
rm /tmp/test.html

echo_color "green" "前端重新部署完成"
echo_color "yellow" "请访问 http://localhost:8080/test.html 测试静态文件服务是否正常"
echo_color "yellow" "然后访问 http://localhost:8080 查看主页是否正常显示"
