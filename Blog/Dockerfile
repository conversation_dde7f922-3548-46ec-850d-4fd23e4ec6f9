# 多阶段构建

# 前端构建阶段
FROM node:18-alpine AS frontend-builder
WORKDIR /app
# 复制整个客户端目录
COPY client/ ./
# 清理 node_modules（如果存在）
RUN rm -rf node_modules
# 使用 CI 模式安装依赖，避免交互式提示
RUN npm ci --legacy-peer-deps || npm install --legacy-peer-deps
# 构建前端
RUN npm run build || (echo "Build failed, checking environment" && ls -la && cat package.json && exit 1)

# 后端构建阶段
FROM golang:1.24-alpine AS backend-builder
WORKDIR /app
# 安装必要的工具
RUN apk add --no-cache git

# 复制 go.mod 和 go.sum 文件
COPY server/go.mod server/go.sum ./
# 修复 go.mod 文件中的版本号（如果需要）
RUN if grep -q "go 1.24" go.mod; then sed -i 's/go 1.24/go 1.20/' go.mod; fi

# 下载依赖
RUN go mod tidy && go mod download
# 复制源代码
COPY server/ ./
# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -o server .
# 确保构建的二进制文件有执行权限
RUN chmod +x server

# 最终镜像
FROM nginx:alpine
WORKDIR /app

# 安装调试工具
RUN apk add --no-cache curl procps

# 复制 Nginx 配置
COPY nginx/default.conf /etc/nginx/conf.d/default.conf

# 复制前端构建产物
COPY --from=frontend-builder /app/build /usr/share/nginx/html

# 复制自定义index.html（如果存在）
COPY custom-index.html /usr/share/nginx/html/index.html.custom
# 使用自定义index.html替换原始index.html（如果存在）
RUN if [ -f /usr/share/nginx/html/index.html.custom ]; then \
    mv /usr/share/nginx/html/index.html.custom /usr/share/nginx/html/index.html; \
    echo "Using custom index.html"; \
    fi

# 创建后端目录
RUN mkdir -p /app/server

# 复制后端构建产物
COPY --from=backend-builder /app/server /app/server
# 确保后端可执行文件有执行权限
RUN chmod +x /app/server

# 复制环境配置文件
COPY server/.env /app/

# 复制启动脚本
COPY docker-entrypoint.sh /app/
RUN chmod +x /app/docker-entrypoint.sh

# 创建上传目录
RUN mkdir -p /app/uploads && chmod 777 /app/uploads
ENV PORT=9000

# 暴露端口
EXPOSE 80 9000

# 设置入口点
ENTRYPOINT ["/app/docker-entrypoint.sh"]

USER root
