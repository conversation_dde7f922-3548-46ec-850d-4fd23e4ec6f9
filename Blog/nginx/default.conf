server {
    listen 80;
    index index.html;
    server_name localhost;

    # 全局安全头设置
    add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:; style-src 'self' 'unsafe-inline' https:;" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;

    # 明确定义静态资源路径
    location /static/ {
        root /usr/share/nginx/html;
        # 禁用缓存，方便调试
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate" always;
        add_header X-Debug-Static-Dir $uri always;
    }

    # 前端静态文件
    location / {
        root /usr/share/nginx/html;
        try_files $uri $uri/ /index.html;
        # 添加调试头信息
        add_header X-Debug-Path $uri always;
        # 禁用缓存，方便调试
        add_header Cache-Control "no-store, no-cache, must-revalidate" always;
    }

    # 后端 API 代理
    location /api/ {
        # 在Docker网络中，使用服务名称而不是localhost
        proxy_pass http://blog-backend:9000/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 添加调试头信息
        add_header X-Debug-Target "http://blog-backend:9000/" always;

        # 添加详细的错误日志
        error_log /var/log/nginx/api_error.log debug;

        # 添加CORS头信息
        add_header Access-Control-Allow-Origin '*' always;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
        add_header Access-Control-Allow-Credentials 'true' always;

        # 处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin '*' always;
            add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS' always;
            add_header Access-Control-Allow-Headers 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
            add_header Access-Control-Allow-Credentials 'true' always;
            add_header Content-Type 'text/plain charset=UTF-8';
            add_header Content-Length 0;
            return 204;
        }

        # 添加调试头信息
        add_header X-Debug-Message "Proxied to backend" always;
    }

    # 上传文件目录
    location /uploads/ {
        alias /app/uploads/;
        autoindex off;
    }

    # 静态资源缓存设置
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        root /usr/share/nginx/html;
        # 在开发阶段禁用缓存
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate" always;
        # 完全禁用SRI检查，解决资源完整性问题
        add_header Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:;" always;
        # 添加调试头信息
        add_header X-Debug-Static-Path $uri always;
    }

    # 错误页面
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}