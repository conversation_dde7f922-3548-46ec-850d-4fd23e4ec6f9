import { BrowserRouter as Router, Route, Routes } from "react-router-dom";
import React, { useEffect } from "react";
import "./App.css";
import "./styles/theme.css"; // 导入主题样式
import Home from "./page/Home";
import Blog from "./page/Blog";
import Add from "./page/Add";
import Edit from "./page/Edit";
import Login from "./page/Login";
import Register from "./page/Register";
import Profile from "./page/Profile";
import About from "./page/About";
import Contact from "./page/Contact";
import Debug from "./page/Debug";
import Header from "./components/layout/Header";
import Footer from "./components/layout/Footer";
import { AuthProvider } from "./context/AuthContext";
import { ThemeProvider, useTheme } from "./context/ThemeContext";

function AppContent() {
  // 使用主题上下文
  const { particleEffect, changeParticleEffect } = useTheme();

  // 使用 useMemo 缓存粒子效果配置
  const particleEffects = React.useMemo(() => ({
    colorful: '/particles.json',
    dream: '/particles-dream.json',
    stars: '/particles-stars.json'
  }), []);

  // 初始化粒子效果
  useEffect(() => {
    if (window.particlesJS) {
      // 清除之前的粒子效果实例
      if (window.pJSDom && window.pJSDom.length > 0) {
        window.pJSDom.forEach(dom => dom.pJS.fn.vendors.destroypJS());
        window.pJSDom = [];
      }

      // 加载新的粒子效果
      window.particlesJS.load('particles-js', particleEffects[particleEffect], function() {
        console.log(`粒子效果 ${particleEffect} 已加载`);
      });
    }
  }, [particleEffect, particleEffects]);

  return (
    <Router>
      {/* 粒子背景容器 */}
      <div id="particles-js"></div>

      {/* 页面布局 */}
      <div className="app-container">
        <Header />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/blog/:id" element={<Blog />} />
            <Route path="/add" element={<Add />} />
            <Route path="/edit/:id" element={<Edit />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/profile" element={<Profile />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/debug" element={<Debug />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
